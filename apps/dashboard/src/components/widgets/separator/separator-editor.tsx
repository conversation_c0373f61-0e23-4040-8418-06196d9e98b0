import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Checkbox, Label, Select, Text } from "@saf/ui"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

const sizeOption: Options = [
  {
    label: "Small",
    value: "small",
  },
  {
    label: "Medium",
    value: "medium",
  },
  {
    label: "Large",
    value: "large",
  },
]

const editorSchema = generalWidgetEditorSchema.merge(
  z.object({
    size: z.enum(createEnumFromOptions(sizeOption)),
    drawLine: z.boolean().optional(),
  }),
)

type EditorSchema = z.infer<typeof editorSchema>
type WidgetType = components["schemas"]["SeparatorWidget"]

export const SeparatorEditor = ({
  data,
  onUpdate,
}: {
  data: WidgetType
  onUpdate: (updatedData: WidgetType) => void
}) => {
  const form = useForm<EditorSchema>({
    resolver: zodResolver(editorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      size: "regular",
      drawLine: false,
    },
  })

  useEffect(() => {
    if (data) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        size: data.config?.design?.size,
        drawLine: data.config?.design?.drawLine ?? false,
      })
    }
  }, [data, form])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: WidgetType = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            design: {
              size: values.size as WidgetType["config"]["design"]["size"],
              drawLine: values.drawLine ?? false,
            },
          },
        }
        console.log("updatedData", updatedData)
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Label className="flex items-center justify-between gap-4">
              <span>Size</span>
              <div className="w-full max-w-[170px]">
                <Field {...form} name="size">
                  <Select onValueChange={form.setValue.bind(null, "size")}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {sizeOption.map((item) => (
                        <Select.Item key={item.value} value={item.value}>
                          {item.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Field>
              </div>
            </Label>
            <Form.Field
              control={form.control}
              name="drawLine"
              render={({ field: { value, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex flex-row items-center gap-3">
                    <Form.Control>
                      <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                    </Form.Control>
                    <Form.Label>Draw Line</Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
