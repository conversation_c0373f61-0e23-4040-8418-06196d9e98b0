import { safQ<PERSON>y } from "@/client"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { SortableList } from "@/components/common/sortable-list"
import { widgetOptionsMap } from "@/configs/widget-options"
import { cn } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Badge, Checkbox, IconButton, Input, Label, Popover, Select, Text, Textarea } from "@saf/ui"
import { IconPlus, IconTrash } from "@tabler/icons-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useParams } from "react-router-dom"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"
import { DotsSix } from "@medusajs/icons"

// Form-compatible widget types (excluding form_container to prevent nesting)
const formWidgetTypes = [
  "text_entry",
  "number_entry",
  "email_entry",
  "phone_entry",
  "date",
  "date_time",
  "time",
  "checkbox",
  "choice",
  "image_picker",
  "file_picker",
  "text",
  "title",
  "separator",
  "image",
] as const

const formContainerEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    // Basic config
    title: z.string().optional(),
    subtitle: z.string().optional(),
    submitButtonText: z.string().optional(),

    // Binding config
    dataSourceId: z.number().optional(),
    externalApiId: z.number().optional(),
  }),
)

type FormContainerEditorSchema = z.infer<typeof formContainerEditorSchema>

export const FormContainerEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["FormContainerWidget"]
  onUpdate: (updatedData: components["schemas"]["FormContainerWidget"]) => void
}) => {
  const { teamId = "", miniAppId = "" } = useParams()

  const [widgetSelectorOpen, setWidgetSelectorOpen] = useState(false)

  const form = useForm<FormContainerEditorSchema>({
    resolver: zodResolver(formContainerEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      title: "",
      subtitle: "",
      submitButtonText: "Submit",
      dataSourceId: undefined,
      externalApiId: undefined,
    },
  })

  // Fetch data sources
  const { data: dataSourcesData } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      enabled: !!teamId && !!miniAppId,
    },
  )

  // Fetch external APIs for the selected data source
  const selectedDataSourceId = form.watch("dataSourceId")
  const { data: externalApisData } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: selectedDataSourceId || 0,
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      enabled: !!teamId && !!miniAppId && !!selectedDataSourceId,
    },
  )

  useEffect(() => {
    if (data) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        title: data.config.title || "",
        subtitle: data.config.subtitle || "",
        submitButtonText: data.config.design?.submitButtonText || "Submit",
        dataSourceId: data.config.bindingConfig?.dataSourceId || undefined,
        externalApiId: data.config.bindingConfig?.externalApiId || undefined,
      })
    }
  }, [data, form])

  useEffect(() => {
    if (!data) return

    const subscription = form.watch((values) => {
      const updatedData: components["schemas"]["FormContainerWidget"] = {
        ...data,
        name: values.name || "",
        isHidden: values.isHidden ?? false,
        config: {
          ...data.config,
          title: values.title,
          subtitle: values.subtitle,
          bindingConfig: values.dataSourceId
            ? {
                dataSourceId: values.dataSourceId,
                externalApiId: values.externalApiId,
              }
            : undefined,
          design: {
            submitButtonText: values.submitButtonText,
          },
        },
      }
      onUpdate(updatedData)
    })
    return () => subscription.unsubscribe()
  }, [data, form, onUpdate])

  // Reset external API when data source changes
  useEffect(() => {
    form.setValue("externalApiId", undefined)
  }, [selectedDataSourceId, form])

  // Prepare dropdown options
  const dataSourceOptions =
    dataSourcesData?.items?.map((ds) => ({
      label: ds.name,
      value: ds.id.toString(),
    })) || []

  const externalApiOptions =
    externalApisData?.items?.map((api) => ({
      label: api.name,
      value: api.id.toString(),
    })) || []

  // Helper function to get default config for widget types
  const getDefaultConfigForWidgetType = (widgetType: string) => {
    switch (widgetType) {
      case "text_entry":
      case "number_entry":
      case "email_entry":
      case "phone_entry":
        return { label: "Label", required: false, binding: "" }
      case "date":
      case "date_time":
      case "time":
        return { label: "Label", required: false, binding: "" }
      case "checkbox":
        return { label: "Label", required: false, binding: "" }
      case "choice":
        return { label: "Label", required: false, binding: "", options: [] }
      case "image_picker":
      case "file_picker":
        return { label: "Label", required: false, binding: "" }
      case "text":
        return { content: "Text content", design: { style: "regular", textAlign: "left" } }
      case "title":
        return { style: "simple", data: { title: "Title" }, design: { imageFill: "contain" } }
      case "separator":
        return { design: { size: "medium", drawLine: true } }
      case "image":
        return { content: "", design: { aspectRatio: "1:1", fill: "fill", fullWidth: false } }
      default:
        return {}
    }
  }

  // Widget management functions
  const handleAddWidget = (widgetType: (typeof formWidgetTypes)[number]) => {
    const newWidget = {
      id: Date.now(), // Temporary ID for local state
      name: `New ${widgetOptionsMap[widgetType as keyof typeof widgetOptionsMap]?.name || widgetType}`,
      isHidden: false,
      widgetType: widgetType,
      config: getDefaultConfigForWidgetType(widgetType),
    } as any // Type assertion for flexibility

    const updatedData: components["schemas"]["FormContainerWidget"] = {
      ...data,
      config: {
        ...data.config,
        widgets: [...(data.config.widgets || []), newWidget],
      },
    }
    onUpdate(updatedData)
    setWidgetSelectorOpen(false)
  }

  const handleRemoveWidget = (widgetId: number) => {
    const updatedData: components["schemas"]["FormContainerWidget"] = {
      ...data,
      config: {
        ...data.config,
        widgets: data.config.widgets?.filter((w) => w.id !== widgetId),
      },
    }
    onUpdate(updatedData)
  }

  const handleReorderWidgets = (newWidgets: any[]) => {
    const updatedData: components["schemas"]["FormContainerWidget"] = {
      ...data,
      config: {
        ...data.config,
        widgets: newWidgets,
      },
    }
    onUpdate(updatedData)
  }

  const handleUpdateWidget = (widgetId: number, updatedWidget: any) => {
    const updatedData: components["schemas"]["FormContainerWidget"] = {
      ...data,
      config: {
        ...data.config,
        widgets: data.config.widgets?.map((w) => (w.id === widgetId ? { ...w, ...updatedWidget } : w)),
      },
    }
    onUpdate(updatedData)
  }

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Form Configuration
          </Text>

          <Field control={form.control} name="title" label="Form Title">
            <Input placeholder="Enter form title..." />
          </Field>

          <Field control={form.control} name="subtitle" label="Form Subtitle">
            <Textarea className="min-h-20" placeholder="Enter form subtitle..." />
          </Field>

          <Field control={form.control} name="submitButtonText" label="Submit Button Text">
            <Input placeholder="Submit" />
          </Field>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Data Binding
          </Text>

          <Label className="flex items-center justify-between gap-4">
            <span>Data Source</span>
            <div className="w-full max-w-[170px]">
              <Field {...form} name="dataSourceId">
                <Select onValueChange={(value) => form.setValue("dataSourceId", value ? parseInt(value) : undefined)}>
                  <Select.Trigger>
                    <Select.Value placeholder="Select data source..." />
                  </Select.Trigger>
                  <Select.Content>
                    {dataSourceOptions.map((item) => (
                      <Select.Item key={item.value} value={item.value}>
                        {item.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Field>
            </div>
          </Label>

          {selectedDataSourceId && (
            <Label className="flex items-center justify-between gap-4">
              <span>External API</span>
              <div className="w-full max-w-[170px]">
                <Field {...form} name="externalApiId">
                  <Select
                    onValueChange={(value) => form.setValue("externalApiId", value ? parseInt(value) : undefined)}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select external API..." />
                    </Select.Trigger>
                    <Select.Content>
                      {externalApiOptions.map((item) => (
                        <Select.Item key={item.value} value={item.value}>
                          {item.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Field>
              </div>
            </Label>
          )}
        </div>

        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center justify-between">
            <Text className="uppercase" size="xsmall" weight="plus">
              Form Fields
            </Text>
            <Popover open={widgetSelectorOpen} onOpenChange={setWidgetSelectorOpen}>
              <Popover.Trigger asChild>
                <IconButton size="xsmall" variant="primary">
                  <IconPlus className="size-4" />
                </IconButton>
              </Popover.Trigger>
              <Popover.Content className="w-64 p-0" side="right">
                <FormWidgetSelector onAddWidget={handleAddWidget} />
              </Popover.Content>
            </Popover>
          </div>

          {data.config.widgets?.length === 0 ? (
            <Text className="px-1.5 py-4 text-center" size="xsmall" color="subtle">
              No form fields added yet. Click the + button to add fields.
            </Text>
          ) : (
            <SortableList
              className="gap-2"
              items={data.config.widgets?.map((w) => ({ id: w.id })) ?? []}
              onChange={(newItems) => {
                const reorderedWidgets =
                  newItems.map((item) => data.config.widgets?.find((w) => w.id === item.id)) || []
                handleReorderWidgets(reorderedWidgets)
              }}
              renderItem={(item) => {
                const widget = data.config.widgets?.find((w) => w.id === item.id) || []
                return (
                  <SortableList.Item id={item.id}>
                    <FormWidgetItem
                      widget={widget}
                      onUpdate={(updatedWidget: any) =>
                        widget && "id" in widget ? handleUpdateWidget(widget.id, updatedWidget) : null
                      }
                      onRemove={() => (widget && "id" in widget ? handleRemoveWidget(widget?.id) : null)}
                    />
                  </SortableList.Item>
                )
              }}
            />
          )}
        </div>
      </form>
    </Form>
  )
}

// Form Widget Selector Component
const FormWidgetSelector = ({
  onAddWidget,
}: {
  onAddWidget: (widgetType: (typeof formWidgetTypes)[number]) => void
}) => {
  const formInputWidgets = formWidgetTypes.filter((type) =>
    [
      "text_entry",
      "number_entry",
      "email_entry",
      "phone_entry",
      "date",
      "date_time",
      "time",
      "checkbox",
      "choice",
      "image_picker",
      "file_picker",
    ].includes(type),
  )

  const formContentWidgets = formWidgetTypes.filter((type) => ["text", "title", "separator", "image"].includes(type))

  return (
    <div className="max-h-[400px] divide-y overflow-y-auto py-2">
      <div className="py-2">
        <Text size="xsmall" className="px-4 uppercase" weight="plus">
          Form Inputs
        </Text>
        <div className="grid grid-cols-2 gap-2 px-1 py-2">
          {formInputWidgets?.map((widgetType) => (
            <button
              type="button"
              key={widgetType}
              onClick={() => onAddWidget(widgetType)}
              className={cn(
                "flex w-full flex-col items-center justify-start gap-1.5 rounded-lg p-2 text-center transition-colors hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed",
              )}
            >
              <div className="text-xs font-medium">{widgetOptionsMap[widgetType]?.name || widgetType}</div>
            </button>
          ))}
        </div>
      </div>

      <div className="py-2">
        <Text size="xsmall" className="px-4 uppercase" weight="plus">
          Content
        </Text>
        <div className="grid grid-cols-2 gap-2 px-1 py-2">
          {formContentWidgets?.map((widgetType) => (
            <button
              type="button"
              key={widgetType}
              onClick={() => onAddWidget(widgetType)}
              className={cn(
                "flex w-full flex-col items-center justify-start gap-1.5 rounded-lg p-2 text-center transition-colors hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed",
              )}
            >
              <div className="text-xs font-medium">{widgetOptionsMap[widgetType]?.name || widgetType}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

// Form Widget Item Component
const FormWidgetItem = ({
  widget,
  onUpdate,
  onRemove,
}: {
  widget: any
  onUpdate: (updatedWidget: any) => void
  onRemove: () => void
}) => {
  const { attributes, listeners, ref } = SortableList.useSortableItemContext()

  const handleConfigChange = (configKey: string, value: any) => {
    onUpdate({
      config: {
        ...widget.config,
        [configKey]: value,
      },
    })
  }

  return (
    <div className="w-full rounded-lg border bg-ui-bg-subtle">
      <div className="flex items-center gap-2 px-1 py-3">
        <IconButton
          type="button"
          variant="transparent"
          size="xsmall"
          {...attributes}
          {...listeners}
          ref={ref}
          className="cursor-grab touch-none active:cursor-grabbing"
        >
          <DotsSix className="cursor-grab touch-none text-ui-fg-muted active:cursor-grabbing" />
        </IconButton>

        <Badge size="xsmall" color="blue">
          {widgetOptionsMap[widget.widgetType as keyof typeof widgetOptionsMap]?.name || widget.widgetType}
        </Badge>
        <IconButton type="button" variant="transparent" size="small" onClick={onRemove} className="ms-auto">
          <IconTrash className="h-4 w-4" />
        </IconButton>
      </div>

      <div className="space-y-3 border-t p-3">
        {/* Basic input widget config */}
        {[
          "text_entry",
          "number_entry",
          "email_entry",
          "phone_entry",
          "date",
          "date_time",
          "time",
          "checkbox",
          "choice",
          "image_picker",
          "file_picker",
        ].includes(widget.widgetType) && (
          <>
            <div>
              <Label className="text-xs font-medium">Label</Label>
              <Input
                value={widget.config?.label || ""}
                onChange={(e) => handleConfigChange("label", e.target.value)}
                placeholder="Field label..."
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs font-medium">Binding</Label>
              <Input
                value={widget.config?.binding || ""}
                onChange={(e) => handleConfigChange("binding", e.target.value)}
                placeholder="Data binding field..."
                className="mt-1"
              />
            </div>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={widget.config?.required || false}
                onCheckedChange={(checked) => handleConfigChange("required", checked)}
              />
              <Label className="text-xs font-medium">Required</Label>
            </div>
          </>
        )}

        {/* Text widget config */}
        {widget.widgetType === "text" && (
          <div>
            <Label className="text-xs font-medium">Content</Label>
            <Textarea
              value={widget.config?.content || ""}
              onChange={(e) => handleConfigChange("content", e.target.value)}
              placeholder="Text content..."
              className="mt-1 min-h-20"
            />
          </div>
        )}

        {/* Title widget config */}
        {widget.widgetType === "title" && (
          <div>
            <Label className="text-xs font-medium">Title</Label>
            <Input
              value={widget.config?.data?.title || ""}
              onChange={(e) => handleConfigChange("data", { ...widget.config?.data, title: e.target.value })}
              placeholder="Title text..."
              className="mt-1"
            />
          </div>
        )}
      </div>
    </div>
  )
}
